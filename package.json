{"name": "aws-quiz", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "import-questions": "tsx scripts/import-aws-questions.ts"}, "repository": {"type": "git", "url": "git+https://github.com/yuankui/aws-quiz.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/yuankui/aws-quiz/issues"}, "homepage": "https://github.com/yuankui/aws-quiz#readme", "description": "", "dependencies": {"@prisma/client": "^6.11.1", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "next": "^15.3.5", "postcss": "^8.5.6", "prettier": "^3.2.5", "prisma": "^6.11.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}