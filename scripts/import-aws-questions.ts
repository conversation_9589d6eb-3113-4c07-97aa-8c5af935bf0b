import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

interface AWSQuizQuestion {
  topic: string;
  question_number: string;
  question_text: string;
  options: string[];
  is_multiple_choice: boolean;
  multiple_choice_count: number;
  correct_answer: string;
  selected_answer: string | null;
}

// Function to categorize questions based on content
function categorizeQuestion(questionText: string, options: string[]): string {
  const text = (questionText + ' ' + options.join(' ')).toLowerCase();

  // Define category keywords
  const categories = {
    Storage: ['s3', 'ebs', 'efs', 'fsx', 'storage', 'glacier', 'backup', 'snapshot'],
    Compute: ['ec2', 'lambda', 'fargate', 'batch', 'elastic beanstalk', 'auto scaling'],
    Database: ['rds', 'dynamodb', 'aurora', 'redshift', 'documentdb', 'neptune', 'database'],
    Networking: [
      'vpc',
      'subnet',
      'route',
      'gateway',
      'load balancer',
      'cloudfront',
      'direct connect',
    ],
    Security: ['iam', 'cognito', 'kms', 'secrets manager', 'certificate manager', 'waf', 'shield'],
    Monitoring: ['cloudwatch', 'cloudtrail', 'x-ray', 'config', 'systems manager'],
    Analytics: ['kinesis', 'glue', 'athena', 'quicksight', 'emr', 'data pipeline'],
    Integration: ['sqs', 'sns', 'eventbridge', 'step functions', 'api gateway'],
    Container: ['ecs', 'eks', 'ecr', 'container', 'docker', 'kubernetes'],
    DevOps: ['codebuild', 'codedeploy', 'codepipeline', 'cloudformation', 'cdk'],
  };

  // Count matches for each category
  const scores: { [key: string]: number } = {};

  for (const [category, keywords] of Object.entries(categories)) {
    scores[category] = keywords.reduce((count, keyword) => {
      return count + (text.includes(keyword) ? 1 : 0);
    }, 0);
  }

  // Find category with highest score
  const bestCategory = Object.entries(scores).reduce((a, b) =>
    scores[a[0]] > scores[b[0]] ? a : b
  )[0];

  return scores[bestCategory] > 0 ? bestCategory : 'General';
}

// Function to determine difficulty based on question characteristics
function determineDifficulty(
  questionText: string,
  options: string[],
  isMultipleChoice: boolean
): string {
  const text = questionText.toLowerCase();
  const optionText = options.join(' ').toLowerCase();

  // Multiple choice questions are generally harder
  if (isMultipleChoice) {
    return 'Hard';
  }

  // Check for complexity indicators
  const hardKeywords = [
    'most cost-effective',
    'least operational overhead',
    'best practice',
    'optimize',
    'troubleshoot',
    'migrate',
    'architect',
    'design',
    'combination',
    'multiple',
    'complex',
    'enterprise',
  ];

  const mediumKeywords = [
    'configure',
    'implement',
    'setup',
    'create',
    'deploy',
    'security',
    'performance',
    'scalability',
    'availability',
  ];

  const hardCount = hardKeywords.reduce(
    (count, keyword) => count + (text.includes(keyword) ? 1 : 0),
    0
  );

  const mediumCount = mediumKeywords.reduce(
    (count, keyword) => count + (text.includes(keyword) ? 1 : 0),
    0
  );

  // Question length can also indicate complexity
  const questionLength = questionText.length;

  if (hardCount >= 2 || questionLength > 500) {
    return 'Hard';
  } else if (hardCount >= 1 || mediumCount >= 2 || questionLength > 300) {
    return 'Medium';
  } else {
    return 'Easy';
  }
}

async function importQuestions() {
  try {
    console.log('🚀 Starting AWS questions import...');

    // Read the JSON file
    const jsonPath = path.join(process.cwd(), 'data', 'aws_quiz_final.json');
    const jsonData = fs.readFileSync(jsonPath, 'utf-8');
    const questions: AWSQuizQuestion[] = JSON.parse(jsonData);

    console.log(`📊 Found ${questions.length} questions to import`);

    // Clear existing questions
    console.log('🧹 Clearing existing questions...');
    await prisma.userAnswer.deleteMany();
    await prisma.examQuestion.deleteMany();
    await prisma.question.deleteMany();

    // Import questions in batches
    const batchSize = 100;
    let imported = 0;

    for (let i = 0; i < questions.length; i += batchSize) {
      const batch = questions.slice(i, i + batchSize);

      const questionsToCreate = batch.map((q, index) => {
        const category = categorizeQuestion(q.question_text, q.options);
        const difficulty = determineDifficulty(q.question_text, q.options, q.is_multiple_choice);

        return {
          topic: q.topic,
          questionNumber: q.question_number,
          questionText: q.question_text,
          options: JSON.stringify(q.options),
          isMultipleChoice: q.is_multiple_choice,
          multipleChoiceCount: q.multiple_choice_count,
          correctAnswer: q.correct_answer,
          selectedAnswer: q.selected_answer,
          category,
          difficulty,
        };
      });

      await prisma.question.createMany({
        data: questionsToCreate,
      });

      imported += batch.length;
      console.log(`✅ Imported ${imported}/${questions.length} questions`);
    }

    // Generate statistics
    const stats = await prisma.question.groupBy({
      by: ['category'],
      _count: {
        id: true,
      },
    });

    const difficultyStats = await prisma.question.groupBy({
      by: ['difficulty'],
      _count: {
        id: true,
      },
    });

    console.log('\n📈 Import Statistics:');
    console.log('Categories:');
    stats.forEach((stat) => {
      console.log(`  ${stat.category}: ${stat._count.id} questions`);
    });

    console.log('\nDifficulties:');
    difficultyStats.forEach((stat) => {
      console.log(`  ${stat.difficulty}: ${stat._count.id} questions`);
    });

    const multipleChoiceCount = await prisma.question.count({
      where: { isMultipleChoice: true },
    });

    const singleChoiceCount = await prisma.question.count({
      where: { isMultipleChoice: false },
    });

    console.log('\nQuestion Types:');
    console.log(`  Multiple Choice: ${multipleChoiceCount} questions`);
    console.log(`  Single Choice: ${singleChoiceCount} questions`);

    console.log(`\n🎉 Successfully imported ${imported} AWS quiz questions!`);
  } catch (error) {
    console.error('❌ Error importing questions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
if (require.main === module) {
  importQuestions()
    .then(() => {
      console.log('✨ Import completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Import failed:', error);
      process.exit(1);
    });
}

export { importQuestions };
