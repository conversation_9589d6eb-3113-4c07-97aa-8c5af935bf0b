// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// AWS Quiz Question model based on data/aws_quiz_final.json structure
model Question {
  id                  Int     @id @default(autoincrement())
  topic               String // e.g., "Topic 1"
  questionNumber      String  @map("question_number") // e.g., "1", "2", etc.
  questionText        String  @map("question_text")
  options             String // JSON string array of options ["A. ...", "B. ...", etc.]
  isMultipleChoice    Boolean @map("is_multiple_choice")
  multipleChoiceCount Int     @map("multiple_choice_count")
  correctAnswer       String  @map("correct_answer") // e.g., "A", "B", "C", "D"
  selectedAnswer      String? @map("selected_answer") // Can be null initially

  // Additional fields for categorization and difficulty
  category   String? // Derived from question content (Storage, Compute, etc.)
  difficulty String? // Derived difficulty (Easy, Medium, Hard)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  examQuestions ExamQuestion[]
  userAnswers   UserAnswer[]

  @@unique([questionNumber])
  @@map("questions")
}

// User model for authentication and exam tracking
model User {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String?

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  exams       Exam[]
  userAnswers UserAnswer[]

  @@map("users")
}

// Exam model for exam sessions
model Exam {
  id          Int     @id @default(autoincrement())
  title       String
  description String?

  // Exam configuration
  questionCount         Int     @map("question_count")
  timeLimit             Int?    @map("time_limit") // in minutes
  categories            String? // JSON string array of selected categories
  difficulties          String? // JSON string array of selected difficulties
  includeMultipleChoice Boolean @default(true) @map("include_multiple_choice")
  includeSingleChoice   Boolean @default(true) @map("include_single_choice")

  // Exam state
  status      String    @default("draft") // draft, active, completed, archived
  startedAt   DateTime? @map("started_at")
  completedAt DateTime? @map("completed_at")

  // Scoring
  totalQuestions Int    @default(0) @map("total_questions")
  correctAnswers Int    @default(0) @map("correct_answers")
  score          Float? // percentage score

  // User relation
  userId Int  @map("user_id")
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  examQuestions ExamQuestion[]
  userAnswers   UserAnswer[]

  @@map("exams")
}

// Junction table for exam questions
model ExamQuestion {
  id         Int @id @default(autoincrement())
  examId     Int @map("exam_id")
  questionId Int @map("question_id")
  order      Int // Question order in the exam

  // Relations
  exam     Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")

  @@unique([examId, questionId])
  @@map("exam_questions")
}

// User answers for exam questions
model UserAnswer {
  id         Int @id @default(autoincrement())
  examId     Int @map("exam_id")
  questionId Int @map("question_id")
  userId     Int @map("user_id")

  // Answer data
  selectedAnswer String?  @map("selected_answer") // User's selected answer
  isCorrect      Boolean? @map("is_correct") // Whether the answer is correct
  timeSpent      Int?     @map("time_spent") // Time spent on question in seconds

  // Relations
  exam     Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([examId, questionId, userId])
  @@map("user_answers")
}
