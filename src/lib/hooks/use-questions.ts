import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  fetchQuestions,
  fetchQuestionsByCategory,
  fetchQuestion,
  createQuizSession,
  submitAnswer,
  completeQuizSession,
  fetchQuizSession,
  type Question,
  type QuizSession,
} from '@/lib/actions/questions';

// Query keys for consistent cache management
export const questionKeys = {
  all: ['questions'] as const,
  lists: () => [...questionKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...questionKeys.lists(), { filters }] as const,
  details: () => [...questionKeys.all, 'detail'] as const,
  detail: (id: string) => [...questionKeys.details(), id] as const,
  categories: (category: string) => [...questionKeys.all, 'category', category] as const,
};

export const quizKeys = {
  all: ['quiz'] as const,
  sessions: () => [...quizKeys.all, 'sessions'] as const,
  session: (id: string) => [...quizKeys.sessions(), id] as const,
};

// Hook to fetch all questions
export function useQuestions() {
  return useQuery({
    queryKey: questionKeys.lists(),
    queryFn: fetchQuestions,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch questions by category
export function useQuestionsByCategory(category: string) {
  return useQuery({
    queryKey: questionKeys.categories(category),
    queryFn: () => fetchQuestionsByCategory(category),
    enabled: !!category,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch a single question
export function useQuestion(id: string) {
  return useQuery({
    queryKey: questionKeys.detail(id),
    queryFn: () => fetchQuestion(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

// Hook to fetch a quiz session
export function useQuizSession(sessionId: string) {
  return useQuery({
    queryKey: quizKeys.session(sessionId),
    queryFn: () => fetchQuizSession(sessionId),
    enabled: !!sessionId,
    staleTime: 1 * 60 * 1000, // 1 minute (quiz sessions change frequently)
  });
}

// Hook to create a new quiz session
export function useCreateQuizSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createQuizSession,
    onSuccess: (newSession) => {
      // Add the new session to the cache
      queryClient.setQueryData(quizKeys.session(newSession.id), newSession);
    },
  });
}

// Hook to submit an answer
export function useSubmitAnswer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      sessionId,
      questionId,
      answer,
    }: {
      sessionId: string;
      questionId: string;
      answer: number;
    }) => submitAnswer(sessionId, questionId, answer),
    onSuccess: (_, variables) => {
      // Invalidate the quiz session to refetch updated data
      queryClient.invalidateQueries({
        queryKey: quizKeys.session(variables.sessionId),
      });
    },
  });
}

// Hook to complete a quiz session
export function useCompleteQuizSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      sessionId,
      answers,
    }: {
      sessionId: string;
      answers: Record<string, number>;
    }) => completeQuizSession(sessionId, answers),
    onSuccess: (completedSession) => {
      // Update the session in the cache
      queryClient.setQueryData(quizKeys.session(completedSession.id), completedSession);
    },
  });
}
