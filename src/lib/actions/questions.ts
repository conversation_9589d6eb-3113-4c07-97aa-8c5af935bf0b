'use server';

import { prisma } from '@/lib/prisma';
import { revalidatePath } from 'next/cache';

// Types for our data structures
export interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation?: string;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface QuizSession {
  id: string;
  questions: Question[];
  currentQuestionIndex: number;
  answers: Record<string, number>;
  score?: number;
  completedAt?: Date;
}

// Helper function to transform Prisma question to our Question type
function transformPrismaQuestion(prismaQuestion: any): Question {
  return {
    id: prismaQuestion.id.toString(),
    question: prismaQuestion.questionText,
    options: JSON.parse(prismaQuestion.options),
    correctAnswer: prismaQuestion.correctAnswer.charCodeAt(0) - 65, // Convert 'A', 'B', 'C', 'D' to 0, 1, 2, 3
    explanation: undefined, // Add explanation logic if available in your schema
    category: prismaQuestion.category,
    difficulty: prismaQuestion.difficulty?.toLowerCase() as 'easy' | 'medium' | 'hard',
  };
}

// Server Action: Fetch all questions
export async function fetchQuestions(): Promise<Question[]> {
  try {
    const prismaQuestions = await prisma.question.findMany({
      orderBy: { questionNumber: 'asc' },
    });

    return prismaQuestions.map(transformPrismaQuestion);
  } catch (error) {
    console.error('Error fetching questions:', error);
    throw new Error('Failed to fetch questions');
  }
}

// Server Action: Fetch questions by category
export async function fetchQuestionsByCategory(category: string): Promise<Question[]> {
  try {
    const prismaQuestions = await prisma.question.findMany({
      where: {
        category: {
          contains: category,
          mode: 'insensitive',
        },
      },
      orderBy: { questionNumber: 'asc' },
    });

    return prismaQuestions.map(transformPrismaQuestion);
  } catch (error) {
    console.error('Error fetching questions by category:', error);
    throw new Error(`Failed to fetch questions for category: ${category}`);
  }
}

// Server Action: Fetch a single question by ID
export async function fetchQuestion(id: string): Promise<Question> {
  try {
    const prismaQuestion = await prisma.question.findUnique({
      where: { id: parseInt(id) },
    });

    if (!prismaQuestion) {
      throw new Error(`Question with ID ${id} not found`);
    }

    return transformPrismaQuestion(prismaQuestion);
  } catch (error) {
    console.error('Error fetching question:', error);
    throw new Error(`Failed to fetch question with ID: ${id}`);
  }
}

// Server Action: Create a new quiz session
export async function createQuizSession(questionIds?: string[]): Promise<QuizSession> {
  try {
    // For now, we'll create a simple in-memory session
    // In a real app, you might want to store this in the database
    let questions: Question[];

    if (questionIds && questionIds.length > 0) {
      // Fetch specific questions
      const prismaQuestions = await prisma.question.findMany({
        where: {
          id: {
            in: questionIds.map((id) => parseInt(id)),
          },
        },
        orderBy: { questionNumber: 'asc' },
      });
      questions = prismaQuestions.map(transformPrismaQuestion);
    } else {
      // Fetch random questions (limit to 10 for demo)
      const prismaQuestions = await prisma.question.findMany({
        take: 10,
        orderBy: { questionNumber: 'asc' },
      });
      questions = prismaQuestions.map(transformPrismaQuestion);
    }

    const session: QuizSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      questions,
      currentQuestionIndex: 0,
      answers: {},
    };

    return session;
  } catch (error) {
    console.error('Error creating quiz session:', error);
    throw new Error('Failed to create quiz session');
  }
}

// Server Action: Submit an answer for a quiz session
export async function submitAnswer(
  sessionId: string,
  questionId: string,
  answer: number
): Promise<{ correct: boolean; explanation?: string }> {
  try {
    const question = await fetchQuestion(questionId);
    const isCorrect = answer === question.correctAnswer;

    // In a real app, you might want to store this answer in the database
    // For now, we'll just return the result

    return {
      correct: isCorrect,
      explanation: question.explanation,
    };
  } catch (error) {
    console.error('Error submitting answer:', error);
    throw new Error('Failed to submit answer');
  }
}

// Server Action: Complete a quiz session and get final score
export async function completeQuizSession(
  sessionId: string,
  answers: Record<string, number>
): Promise<QuizSession> {
  try {
    // Calculate score based on answers
    let correctCount = 0;
    const questionIds = Object.keys(answers);

    for (const questionId of questionIds) {
      const question = await fetchQuestion(questionId);
      if (answers[questionId] === question.correctAnswer) {
        correctCount++;
      }
    }

    const score = questionIds.length > 0 ? (correctCount / questionIds.length) * 100 : 0;

    // In a real app, you might fetch the session from database and update it
    // For now, we'll create a completed session object
    const completedSession: QuizSession = {
      id: sessionId,
      questions: [], // You might want to fetch the original questions
      currentQuestionIndex: questionIds.length,
      answers,
      score,
      completedAt: new Date(),
    };

    // Revalidate any cached data
    revalidatePath('/');

    return completedSession;
  } catch (error) {
    console.error('Error completing quiz session:', error);
    throw new Error('Failed to complete quiz session');
  }
}

// Server Action: Fetch quiz session by ID
export async function fetchQuizSession(sessionId: string): Promise<QuizSession> {
  try {
    // In a real app, you would fetch this from the database
    // For now, we'll throw an error since we don't store sessions
    throw new Error('Session storage not implemented yet');
  } catch (error) {
    console.error('Error fetching quiz session:', error);
    throw new Error(`Failed to fetch quiz session with ID: ${sessionId}`);
  }
}
