'use client';

import { useState } from 'react';

// 模拟考试历史数据
const mockExamHistory = [
  {
    id: '1',
    date: '2024-01-15',
    score: 85,
    totalQuestions: 20,
    correctAnswers: 17,
    duration: '25分钟',
    category: 'AWS Solutions Architect',
    status: 'completed',
  },
  {
    id: '2',
    date: '2024-01-12',
    score: 72,
    totalQuestions: 15,
    correctAnswers: 11,
    duration: '18分钟',
    category: 'AWS Developer',
    status: 'completed',
  },
  {
    id: '3',
    date: '2024-01-10',
    score: 91,
    totalQuestions: 25,
    correctAnswers: 23,
    duration: '32分钟',
    category: 'AWS SysOps',
    status: 'completed',
  },
  {
    id: '4',
    date: '2024-01-08',
    score: 68,
    totalQuestions: 20,
    correctAnswers: 14,
    duration: '22分钟',
    category: 'AWS Solutions Architect',
    status: 'completed',
  },
];

function getScoreColor(score: number) {
  if (score >= 80) return 'text-green-600 bg-green-100';
  if (score >= 70) return 'text-yellow-600 bg-yellow-100';
  return 'text-red-600 bg-red-100';
}

function getScoreIcon(score: number) {
  if (score >= 80) return '🎉';
  if (score >= 70) return '👍';
  return '📚';
}

export default function ExamHistory() {
  const [sortBy, setSortBy] = useState<'date' | 'score'>('date');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  const categories = ['all', ...Array.from(new Set(mockExamHistory.map((exam) => exam.category)))];

  const filteredAndSortedHistory = mockExamHistory
    .filter((exam) => filterCategory === 'all' || exam.category === filterCategory)
    .sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
      return b.score - a.score;
    });

  const averageScore = Math.round(
    mockExamHistory.reduce((sum, exam) => sum + exam.score, 0) / mockExamHistory.length
  );

  const totalExams = mockExamHistory.length;
  const passedExams = mockExamHistory.filter((exam) => exam.score >= 70).length;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 lg:px-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">考试历史</h1>
          <p className="text-lg text-gray-600">查看您的考试记录和成绩统计</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总考试次数</p>
                <p className="text-2xl font-bold text-gray-900">{totalExams}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🎯</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">平均分数</p>
                <p className="text-2xl font-bold text-gray-900">{averageScore}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">通过次数</p>
                <p className="text-2xl font-bold text-gray-900">{passedExams}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📈</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">通过率</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round((passedExams / totalExams) * 100)}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 筛选和排序 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-wrap gap-4 items-center">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">排序方式</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'date' | 'score')}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="date">按日期排序</option>
                <option value="score">按分数排序</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">筛选分类</label>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category === 'all' ? '全部分类' : category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 考试历史列表 */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">考试记录</h2>
          </div>

          <div className="divide-y divide-gray-200">
            {filteredAndSortedHistory.map((exam) => (
              <div key={exam.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      <span className="text-2xl">{getScoreIcon(exam.score)}</span>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{exam.category}</h3>
                        <p className="text-sm text-gray-500">
                          {new Date(exam.date).toLocaleDateString('zh-CN')} • {exam.duration}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    <div className="text-right">
                      <p className="text-sm text-gray-500">正确率</p>
                      <p className="text-lg font-medium text-gray-900">
                        {exam.correctAnswers}/{exam.totalQuestions}
                      </p>
                    </div>

                    <div
                      className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(exam.score)}`}
                    >
                      {exam.score}%
                    </div>

                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {filteredAndSortedHistory.length === 0 && (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <span className="text-6xl mb-4 block">📝</span>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无考试记录</h3>
            <p className="text-gray-500">开始您的第一次考试吧！</p>
          </div>
        )}
      </div>
    </div>
  );
}
