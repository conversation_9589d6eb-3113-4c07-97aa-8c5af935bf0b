'use client';

import { useState } from 'react';
import { useCreateQuizSession, useCompleteQuizSession } from '@/lib/hooks/use-questions';

export function QuizStarter() {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [answers, setAnswers] = useState<Record<string, number>>({});

  const createSession = useCreateQuizSession();
  const completeSession = useCompleteQuizSession();

  const handleStartQuiz = async () => {
    try {
      const session = await createSession.mutateAsync();
      setSessionId(session.id);
      console.log('Quiz session created:', session);
    } catch (error) {
      console.error('Failed to create quiz session:', error);
    }
  };

  const handleCompleteQuiz = async () => {
    if (!sessionId) return;

    try {
      const completedSession = await completeSession.mutateAsync({
        sessionId,
        answers,
      });
      console.log('Quiz completed:', completedSession);
      alert(`Quiz completed! Score: ${completedSession.score?.toFixed(1)}%`);
    } catch (error) {
      console.error('Failed to complete quiz:', error);
    }
  };

  const handleAnswerChange = (questionId: string, answer: number) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: answer,
    }));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Quiz Session Manager</h2>

      <div className="space-y-4">
        {!sessionId ? (
          <div>
            <p className="text-gray-600 mb-4">Start a new quiz session to begin practicing.</p>
            <button
              onClick={handleStartQuiz}
              disabled={createSession.isPending}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              {createSession.isPending ? 'Creating Session...' : 'Start New Quiz'}
            </button>
          </div>
        ) : (
          <div>
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">Quiz Session Active</h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>Session ID: {sessionId}</p>
                    <p>Answers recorded: {Object.keys(answers).length}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Simulate answering questions (for demo):
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleAnswerChange('1', 0)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded text-sm"
                  >
                    Answer Q1: A
                  </button>
                  <button
                    onClick={() => handleAnswerChange('2', 1)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded text-sm"
                  >
                    Answer Q2: B
                  </button>
                  <button
                    onClick={() => handleAnswerChange('3', 2)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded text-sm"
                  >
                    Answer Q3: C
                  </button>
                  <button
                    onClick={() => handleAnswerChange('4', 3)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded text-sm"
                  >
                    Answer Q4: D
                  </button>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleCompleteQuiz}
                  disabled={completeSession.isPending || Object.keys(answers).length === 0}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  {completeSession.isPending ? 'Completing...' : 'Complete Quiz'}
                </button>

                <button
                  onClick={() => {
                    setSessionId(null);
                    setAnswers({});
                  }}
                  className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  Reset Session
                </button>
              </div>
            </div>
          </div>
        )}

        {(createSession.error || completeSession.error) && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{createSession.error?.message || completeSession.error?.message}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
