# React Query with Server Actions Setup Guide

This document explains how React Query (TanStack Query) has been integrated with Next.js Server Actions in the AWS Quiz application.

## Overview

React Query has been successfully integrated with Next.js Server Actions, providing:

- Automatic caching and background updates
- Loading and error states
- Optimistic updates
- Request deduplication
- Offline support
- Development tools for debugging
- Server-side data fetching with Server Actions
- Type-safe data operations

## Installation

The following packages have been installed:

- `@tanstack/react-query` - Core React Query library
- `@tanstack/react-query-devtools` - Development tools for debugging queries

## Project Structure

```
src/
├── lib/
│   ├── providers/
│   │   └── query-provider.tsx     # Query client provider component
│   ├── hooks/
│   │   └── use-questions.ts       # Custom React Query hooks
│   ├── actions/
│   │   └── questions.ts           # Server Actions
│   └── query-client.ts            # Query client configuration
└── components/
    └── quiz/
        ├── question-list.tsx      # Example component using React Query
        └── quiz-starter.tsx       # Quiz session management component
```

## Configuration

### Query Client Configuration

The query client is configured with sensible defaults in `src/lib/query-client.ts`:

- **Stale Time**: 5 minutes (data is considered fresh for 5 minutes)
- **Cache Time**: 10 minutes (inactive queries are garbage collected after 10 minutes)
- **Retry**: 3 attempts with exponential backoff
- **Refetch on Window Focus**: Disabled
- **Refetch on Reconnect**: Enabled

### Provider Setup

The `QueryProvider` component wraps the entire application in `src/app/layout.tsx` and includes:

- Query client provider
- Development tools (only in development mode)

## Usage Examples

### Basic Query Hook

```typescript
import { useQuestions } from '@/lib/hooks/use-questions';

function QuestionList() {
  const { data: questions, isLoading, error } = useQuestions();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {questions?.map(question => (
        <div key={question.id}>{question.question}</div>
      ))}
    </div>
  );
}
```

### Mutation Hook

```typescript
import { useCreateQuizSession } from '@/lib/hooks/use-questions';

function StartQuizButton() {
  const createSession = useCreateQuizSession();

  const handleStartQuiz = () => {
    createSession.mutate(undefined, {
      onSuccess: (session) => {
        console.log('Quiz session created:', session.id);
      },
      onError: (error) => {
        console.error('Failed to create session:', error);
      }
    });
  };

  return (
    <button
      onClick={handleStartQuiz}
      disabled={createSession.isPending}
    >
      {createSession.isPending ? 'Creating...' : 'Start Quiz'}
    </button>
  );
}
```

### Query with Parameters

```typescript
import { useQuestionsByCategory } from '@/lib/hooks/use-questions';

function CategoryQuestions({ category }: { category: string }) {
  const { data: questions, isLoading } = useQuestionsByCategory(category);

  // Query is automatically enabled/disabled based on category presence
  // and will refetch when category changes

  return (
    <div>
      {isLoading ? 'Loading...' : `${questions?.length || 0} questions`}
    </div>
  );
}
```

## Available Hooks

### Query Hooks

- `useQuestions()` - Fetch all questions
- `useQuestionsByCategory(category)` - Fetch questions by category
- `useQuestion(id)` - Fetch a single question
- `useQuizSession(sessionId)` - Fetch a quiz session

### Mutation Hooks

- `useCreateQuizSession()` - Create a new quiz session
- `useSubmitAnswer()` - Submit an answer for a question
- `useCompleteQuizSession()` - Complete a quiz session

## Query Keys

Query keys are centrally managed for consistency:

```typescript
// Question-related queries
questionKeys.all; // ['questions']
questionKeys.lists(); // ['questions', 'list']
questionKeys.categories(category); // ['questions', 'category', category]
questionKeys.detail(id); // ['questions', 'detail', id]

// Quiz-related queries
quizKeys.all; // ['quiz']
quizKeys.sessions(); // ['quiz', 'sessions']
quizKeys.session(id); // ['quiz', 'sessions', id]
```

## Development Tools

React Query DevTools are automatically included in development mode. You can:

- View all queries and their states
- Inspect query data and metadata
- Manually trigger refetches
- Clear query cache
- Monitor network requests

Access the DevTools by looking for the React Query icon in the bottom corner of your browser.

## Best Practices

1. **Use Query Keys Consistently**: Always use the predefined query keys from the hooks file
2. **Handle Loading States**: Always handle `isLoading` and `error` states in your components
3. **Optimize with Stale Time**: Set appropriate `staleTime` for different types of data
4. **Invalidate Wisely**: Use `queryClient.invalidateQueries()` after mutations that affect cached data
5. **Enable/Disable Queries**: Use the `enabled` option to control when queries should run

## Next Steps

To fully utilize React Query in your application:

1. **Create API Routes**: Implement the actual API endpoints that the API functions call
2. **Add More Hooks**: Create additional hooks for other data operations
3. **Implement Optimistic Updates**: Add optimistic updates for better user experience
4. **Add Error Boundaries**: Implement error boundaries for better error handling
5. **Configure Persistence**: Add query persistence for offline support

## Troubleshooting

### Common Issues

- **Hydration Errors**: Make sure the QueryProvider is only rendered on the client side
- **Stale Data**: Check your `staleTime` and `gcTime` configurations
- **Memory Leaks**: Ensure queries are properly cleaned up when components unmount

### Debug Tips

- Use React Query DevTools to inspect query states
- Check the browser's Network tab for API calls
- Use `console.log` in query functions to debug data flow
