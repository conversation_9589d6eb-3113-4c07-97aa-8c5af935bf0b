# Prisma Types Refactor

## 概述

我们已经成功重构了代码，使用 Prisma 生成的类型而不是重复定义自定义类型。这提高了类型安全性，减少了代码重复，并确保了与数据库模式的一致性。

## 主要变更

### 1. 使用 Prisma 生成的类型

**之前：**

```typescript
// 自定义类型定义
export interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation?: string;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}
```

**之后：**

```typescript
// 使用 Prisma 类型并扩展
import type { Question } from '@prisma/client';

export type QuestionWithParsedOptions = Question & {
  parsedOptions: string[];
  correctAnswerIndex: number;
};
```

### 2. 类型转换函数

创建了一个辅助函数来转换 Prisma 数据：

```typescript
function transformPrismaQuestion(prismaQuestion: Question): QuestionWithParsedOptions {
  return {
    ...prismaQuestion,
    parsedOptions: JSON.parse(prismaQuestion.options),
    correctAnswerIndex: prismaQuestion.correctAnswer.charCodeAt(0) - 65,
  };
}
```

### 3. 更新的 Server Actions

所有 Server Actions 现在返回 `QuestionWithParsedOptions[]` 而不是自定义的 `Question[]`：

```typescript
export async function fetchQuestions(): Promise<QuestionWithParsedOptions[]> {
  const prismaQuestions = await prisma.question.findMany({
    orderBy: { questionNumber: 'asc' },
  });
  return prismaQuestions.map(transformPrismaQuestion);
}
```

### 4. 组件更新

组件现在使用 Prisma 字段名：

```typescript
// 之前
<p>{question.question}</p>
{question.options.map((option, index) => (
  <div className={index === question.correctAnswer ? 'correct' : ''}>
    {option}
  </div>
))}

// 之后
<p>{question.questionText}</p>
{question.parsedOptions.map((option, index) => (
  <div className={index === question.correctAnswerIndex ? 'correct' : ''}>
    {option}
  </div>
))}
```

## 优势

### 1. 类型安全

- 直接使用 Prisma 生成的类型确保与数据库模式的一致性
- TypeScript 编译器可以检测到字段名的变更
- 减少运行时错误

### 2. 代码维护性

- 单一数据源：Prisma 模式是唯一的类型定义来源
- 自动同步：当数据库模式更改时，类型会自动更新
- 减少重复：不需要手动维护重复的类型定义

### 3. 开发体验

- 更好的 IDE 支持和自动完成
- 编译时类型检查
- 重构时的安全性

## 文件结构

```
src/
├── lib/
│   ├── actions/
│   │   └── questions.ts          # 使用 Prisma 类型的 Server Actions
│   ├── hooks/
│   │   └── use-questions.ts      # React Query hooks
│   └── prisma.ts                 # Prisma 客户端
├── components/
│   └── quiz/
│       ├── question-list.tsx     # 使用新类型的组件
│       └── quiz-starter.tsx      # 测验会话管理
└── prisma/
    └── schema.prisma             # 数据库模式定义
```

## 类型映射

| Prisma 字段               | 自定义字段                | 说明         |
| ------------------------- | ------------------------- | ------------ |
| `questionText`            | `question`                | 问题文本     |
| `options` (JSON string)   | `options` (string[])      | 选项数组     |
| `correctAnswer` (A/B/C/D) | `correctAnswer` (0/1/2/3) | 正确答案索引 |
| `category`                | `category`                | 问题分类     |
| `difficulty`              | `difficulty`              | 难度等级     |

## 最佳实践

1. **始终使用 Prisma 类型作为基础**
2. **通过类型扩展添加计算字段**
3. **使用转换函数处理数据格式差异**
4. **保持组件与 Prisma 字段名的一致性**
5. **利用 TypeScript 的类型检查优势**

## 未来改进

1. **添加解释字段**：在 Prisma 模式中添加 `explanation` 字段
2. **优化查询**：使用 Prisma 的 `select` 和 `include` 优化查询性能
3. **缓存策略**：利用 Prisma 的查询缓存功能
4. **类型生成**：考虑使用 Prisma 的类型生成器创建更复杂的类型
