# AWS Quiz

## Code Quality Tools

This project uses <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to ensure code quality and consistent formatting.

### ESLint

ESLint is configured to enforce code quality rules and integrate with <PERSON><PERSON><PERSON> for formatting.

To run ESLint:

```bash
pnpm lint
# or
npm run lint
```

### Prettier

Prettier is used for consistent code formatting across the project.

To format all files:

```bash
pnpm format
# or
npm run format
```

To check if files are formatted correctly without modifying them:

```bash
pnpm format:check
# or
npm run format:check
```

### VS Code Integration

If you're using VS Code, the project includes settings to:

1. Format files on save
2. Run ESLint fix on save
3. Use Pre<PERSON>er as the default formatter

Make sure you have the following extensions installed:

- ESLint
- Prettier - Code formatter

## Development

To run the development server:

```bash
pnpm dev
# or
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
