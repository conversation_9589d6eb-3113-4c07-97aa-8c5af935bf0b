#!/usr/bin/env python3
"""
验证清理后的AWS题库数据质量
"""

import json
import re
from collections import defaultdict

def validate_data():
    print("正在验证清理后的数据质量...")
    
    # 读取清理后的数据
    with open("aws_quiz_cleaned.json", "r", encoding="utf-8") as f:
        questions = json.load(f)
    
    print(f"总题目数量: {len(questions)}")
    
    # 验证统计
    issues = defaultdict(list)
    
    for i, question in enumerate(questions):
        question_num = i + 1
        
        # 检查必要字段
        required_fields = ['topic', 'question_number', 'question_text', 'options', 'is_multiple_choice']
        for field in required_fields:
            if field not in question:
                issues['missing_fields'].append(f"题目 {question_num}: 缺少字段 {field}")
        
        # 检查题目文本
        if 'question_text' in question:
            text = question['question_text']
            if not text.strip():
                issues['empty_question'].append(f"题目 {question_num}: 题目文本为空")
            
            # 检查是否还有特殊字符
            if '\u0000' in text:
                issues['special_chars'].append(f"题目 {question_num}: 仍包含空字符")
            
            # 检查常见错误
            if 'ample' in text and 'Simple' not in text:
                issues['service_name_errors'].append(f"题目 {question_num}: 可能的服务名称错误 - {text[:100]}...")
        
        # 检查选项
        if 'options' in question:
            options = question['options']
            if len(options) < 2:
                issues['insufficient_options'].append(f"题目 {question_num}: 选项少于2个")
            
            for j, option in enumerate(options):
                if not option.strip():
                    issues['empty_options'].append(f"题目 {question_num} 选项 {j+1}: 选项为空")
                
                # 检查选项格式
                if not re.match(r'^[A-F]\.', option.strip()):
                    issues['option_format'].append(f"题目 {question_num} 选项 {j+1}: 格式错误 - {option[:50]}...")
                
                # 检查特殊字符
                if '\u0000' in option:
                    issues['option_special_chars'].append(f"题目 {question_num} 选项 {j+1}: 包含特殊字符")
        
        # 检查多选题逻辑
        if question.get('is_multiple_choice', False):
            if question.get('multiple_choice_count', 0) <= 1:
                issues['multiple_choice_logic'].append(f"题目 {question_num}: 多选题但选择数量 <= 1")
    
    # 输出验证结果
    print("\n=== 数据质量验证结果 ===")
    
    if not any(issues.values()):
        print("✅ 数据质量良好，未发现问题！")
    else:
        for issue_type, issue_list in issues.items():
            if issue_list:
                print(f"\n❌ {issue_type} ({len(issue_list)} 个问题):")
                for issue in issue_list[:5]:  # 只显示前5个
                    print(f"  - {issue}")
                if len(issue_list) > 5:
                    print(f"  ... 还有 {len(issue_list) - 5} 个类似问题")
    
    # 统计信息
    print(f"\n=== 统计信息 ===")
    single_choice = sum(1 for q in questions if not q.get('is_multiple_choice', False))
    multiple_choice = sum(1 for q in questions if q.get('is_multiple_choice', False))
    
    print(f"单选题: {single_choice}")
    print(f"多选题: {multiple_choice}")
    
    # 选项数量分布
    option_counts = defaultdict(int)
    for q in questions:
        option_count = len(q.get('options', []))
        option_counts[option_count] += 1
    
    print(f"\n选项数量分布:")
    for count in sorted(option_counts.keys()):
        print(f"  {count} 个选项: {option_counts[count]} 题")
    
    # 多选题分析
    if multiple_choice > 0:
        choice_counts = defaultdict(int)
        for q in questions:
            if q.get('is_multiple_choice', False):
                count = q.get('multiple_choice_count', 0)
                choice_counts[count] += 1
        
        print(f"\n多选题选择数量分布:")
        for count in sorted(choice_counts.keys()):
            print(f"  选择 {count} 个: {choice_counts[count]} 题")
    
    # 显示一些清理后的示例
    print(f"\n=== 清理后的示例 ===")
    for i in range(min(3, len(questions))):
        q = questions[i]
        print(f"\n题目 {i+1}: {q['topic']} Question #{q['question_number']}")
        print(f"类型: {'多选题' if q['is_multiple_choice'] else '单选题'}")
        if q['is_multiple_choice']:
            print(f"选择数量: {q['multiple_choice_count']}")
        print(f"题目: {q['question_text'][:150]}...")
        print(f"选项:")
        for option in q['options'][:3]:
            print(f"  {option[:100]}...")

if __name__ == "__main__":
    validate_data()
