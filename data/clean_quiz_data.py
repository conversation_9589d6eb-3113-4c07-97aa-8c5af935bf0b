#!/usr/bin/env python3
"""
清理AWS题库数据中的特殊字符和格式问题
"""

import json
import re
from typing import Dict, Any

def clean_text(text: str) -> str:
    """清理文本中的特殊字符和格式问题"""
    if not text:
        return text
    
    # 替换常见的PDF解析错误
    replacements = {
        '\u0000': 'fi',  # 空字符通常是 "fi" 连字符
        'Con\u0000gure': 'Configure',
        'pro\u0000le': 'profile',
        '\u0000le': 'file',
        '\u0000les': 'files',
        'tra\u0000c': 'traffic',
        'certi\u0000cate': 'certificate',
        'noti\u0000cation': 'notification',
        'inde\u0000nitely': 'indefinitely',
        'con\u0000dential': 'confidential',
        'speci\u0000c': 'specific',
        'identi\u0000er': 'identifier',
        'work\u0000ow': 'workflow',
        'e\u0000cient': 'efficient',
        'a\u0000nity': 'affinity',
        'ample': 'Simple',  # "ample" 通常是 "Simple" 的错误解析
        'ample Noti\u0000cation Service': 'Simple Notification Service',
        'Amazon ample': 'Amazon Simple',
    }
    
    # 应用替换
    cleaned_text = text
    for old, new in replacements.items():
        cleaned_text = cleaned_text.replace(old, new)
    
    # 修复常见的AWS服务名称错误
    service_fixes = {
        r'Amazon ample Queue Service': 'Amazon Simple Queue Service',
        r'Amazon ample Storage Service': 'Amazon Simple Storage Service',
        r'Amazon ample Notification Service': 'Amazon Simple Notification Service',
        r'Amazon ample Email Service': 'Amazon Simple Email Service',
        r'AWS ample': 'AWS Simple',
        r'ample Queue Service \(Amazon SQS\)': 'Simple Queue Service (Amazon SQS)',
        r'ample Storage Service \(Amazon S3\)': 'Simple Storage Service (Amazon S3)',
        r'ample Notification Service \(Amazon SNS\)': 'Simple Notification Service (Amazon SNS)',
        r'ample Email Service \(Amazon SES\)': 'Simple Email Service (Amazon SES)',
        r'Amazon SOS\)': 'Amazon SQS)',
        r'Amazon SOS': 'Amazon SQS',
        r'\(Amazon SOS\)': '(Amazon SQS)',
        r'Cross- Region': 'Cross-Region',
        r'Multi- AZ': 'Multi-AZ',
        r'on- premises': 'on-premises',
        r'on- demand': 'on-demand',
        r'real- time': 'real-time',
        r'third- party': 'third-party',
        r'client- side': 'client-side',
        r'server- side': 'server-side',
        r'cross- Region': 'cross-Region',
        r'multi- tier': 'multi-tier',
        r'two- tier': 'two-tier',
        r'three- tier': 'three-tier',
    }
    
    for pattern, replacement in service_fixes.items():
        cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)
    
    # 清理多余的空格
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = cleaned_text.strip()
    
    # 修复一些常见的单词拼写错误
    word_fixes = {
        r'\bcon\s*gure\b': 'configure',
        r'\bpro\s*le\b': 'profile',
        r'\btra\s*c\b': 'traffic',
        r'\bcerti\s*cate\b': 'certificate',
        r'\bnoti\s*cation\b': 'notification',
        r'\bspeci\s*c\b': 'specific',
        r'\bidenti\s*er\b': 'identifier',
        r'\bwork\s*ow\b': 'workflow',
        r'\be\s*cient\b': 'efficient',
        r'\ba\s*nity\b': 'affinity',
    }
    
    for pattern, replacement in word_fixes.items():
        cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)
    
    return cleaned_text

def clean_question_data(question: Dict[str, Any]) -> Dict[str, Any]:
    """清理单个题目的数据"""
    cleaned_question = question.copy()
    
    # 清理题目文本
    if 'question_text' in cleaned_question:
        cleaned_question['question_text'] = clean_text(cleaned_question['question_text'])
    
    # 清理选项
    if 'options' in cleaned_question:
        cleaned_options = []
        for option in cleaned_question['options']:
            cleaned_option = clean_text(option)
            # 确保选项以正确的字母开头
            if cleaned_option and not re.match(r'^[A-F]\.', cleaned_option):
                # 如果选项没有以字母开头，尝试修复
                if len(cleaned_option) > 0:
                    # 检查是否只是缺少了选项标识
                    continue
            cleaned_options.append(cleaned_option)
        cleaned_question['options'] = cleaned_options
    
    return cleaned_question

def main():
    print("正在清理AWS题库数据...")
    
    # 读取原始数据
    with open("aws_quiz_simplified.json", "r", encoding="utf-8") as f:
        questions = json.load(f)
    
    print(f"原始题目数量: {len(questions)}")
    
    # 清理数据
    cleaned_questions = []
    for i, question in enumerate(questions):
        try:
            cleaned_question = clean_question_data(question)
            cleaned_questions.append(cleaned_question)
        except Exception as e:
            print(f"清理第 {i+1} 题时出错: {e}")
            # 保留原始数据
            cleaned_questions.append(question)
    
    print(f"清理后题目数量: {len(cleaned_questions)}")
    
    # 保存清理后的数据
    with open("aws_quiz_cleaned.json", "w", encoding="utf-8") as f:
        json.dump(cleaned_questions, f, ensure_ascii=False, indent=2)
    
    print("清理完成！结果保存到 aws_quiz_cleaned.json")
    
    # 显示一些清理示例
    print("\n清理示例:")
    for i, (original, cleaned) in enumerate(zip(questions[:3], cleaned_questions[:3])):
        if original['question_text'] != cleaned['question_text']:
            print(f"\n题目 {i+1} 清理前:")
            print(f"  {original['question_text'][:100]}...")
            print(f"题目 {i+1} 清理后:")
            print(f"  {cleaned['question_text'][:100]}...")
        
        # 检查选项清理
        for j, (orig_opt, clean_opt) in enumerate(zip(original['options'], cleaned['options'])):
            if orig_opt != clean_opt:
                print(f"  选项 {j+1} 清理前: {orig_opt[:80]}...")
                print(f"  选项 {j+1} 清理后: {clean_opt[:80]}...")
                break

if __name__ == "__main__":
    main()
