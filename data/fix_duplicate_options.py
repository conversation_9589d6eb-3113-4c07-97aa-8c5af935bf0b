#!/usr/bin/env python3
"""
修复重复选项问题
"""

import json
from collections import OrderedDict

def fix_duplicate_options(questions):
    """修复重复选项"""
    fixed_questions = []
    
    for question in questions:
        fixed_question = question.copy()
        
        # 去除重复选项，保持顺序
        seen_options = OrderedDict()
        for option in question['options']:
            # 提取选项字母
            if option and len(option) > 2 and option[1] == '.':
                option_letter = option[0]
                option_text = option[2:].strip()
                
                # 如果这个字母的选项还没有见过，或者当前选项更长（更完整），则保留
                if option_letter not in seen_options or len(option_text) > len(seen_options[option_letter][2:].strip()):
                    seen_options[option_letter] = option
        
        # 重新构建选项列表
        fixed_question['options'] = list(seen_options.values())
        fixed_questions.append(fixed_question)
    
    return fixed_questions

def main():
    print("正在修复重复选项...")
    
    # 读取数据
    with open("aws_quiz_with_answers.json", "r", encoding="utf-8") as f:
        questions = json.load(f)
    
    print(f"原始题目数量: {len(questions)}")
    
    # 修复重复选项
    fixed_questions = fix_duplicate_options(questions)
    
    # 统计修复情况
    fixed_count = 0
    for i, (original, fixed) in enumerate(zip(questions, fixed_questions)):
        if len(original['options']) != len(fixed['options']):
            fixed_count += 1
            print(f"题目 {i+1}: 选项从 {len(original['options'])} 个减少到 {len(fixed['options'])} 个")
    
    print(f"修复了 {fixed_count} 道题目的重复选项")
    
    # 保存修复后的数据
    with open("aws_quiz_final.json", "w", encoding="utf-8") as f:
        json.dump(fixed_questions, f, ensure_ascii=False, indent=2)
    
    print("修复完成！结果保存到 aws_quiz_final.json")
    
    # 统计最终结果
    single_choice = sum(1 for q in fixed_questions if not q['is_multiple_choice'])
    multiple_choice = sum(1 for q in fixed_questions if q['is_multiple_choice'])
    with_answers = sum(1 for q in fixed_questions if q['correct_answer'])
    
    print(f"\n最终统计:")
    print(f"总题目数: {len(fixed_questions)}")
    print(f"单选题: {single_choice}")
    print(f"多选题: {multiple_choice}")
    print(f"有正确答案: {with_answers}")
    
    # 选项数量分布
    option_counts = {}
    for q in fixed_questions:
        count = len(q['options'])
        option_counts[count] = option_counts.get(count, 0) + 1
    
    print(f"\n选项数量分布:")
    for count in sorted(option_counts.keys()):
        print(f"  {count} 个选项: {option_counts[count]} 题")
    
    # 显示修复示例
    print(f"\n修复示例:")
    for i, (original, fixed) in enumerate(zip(questions[:3], fixed_questions[:3])):
        if len(original['options']) != len(fixed['options']):
            print(f"\n题目 {i+1} 修复前选项数: {len(original['options'])}")
            print(f"题目 {i+1} 修复后选项数: {len(fixed['options'])}")
            print(f"正确答案: {fixed['correct_answer']}")
            for opt in fixed['options']:
                print(f"  {opt[:80]}...")

if __name__ == "__main__":
    main()
