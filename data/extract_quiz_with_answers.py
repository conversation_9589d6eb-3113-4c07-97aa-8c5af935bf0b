#!/usr/bin/env python3
"""
AWS Quiz PDF Parser with Answers
解析AWS SAA-C03题库PDF文件，提取题目、选项、答案和多选信息
"""

import re
import json
from typing import List, Dict, Optional
import sys

try:
    import pdfplumber
    USE_PDFPLUMBER = True
except ImportError:
    try:
        import PyPDF2
        USE_PDFPLUMBER = False
    except ImportError:
        print("请安装 pdfplumber 或 PyPDF2: pip install pdfplumber")
        sys.exit(1)

class QuizQuestion:
    def __init__(self):
        self.topic = ""
        self.question_number = ""
        self.question_text = ""
        self.options = []
        self.is_multiple_choice = False
        self.multiple_choice_count = 0
        self.correct_answer = ""
        self.selected_answer = ""
        self.raw_text = ""

def extract_text_from_pdf(pdf_path: str) -> List[str]:
    """从PDF中提取所有页面的文本"""
    pages_text = []
    
    if USE_PDFPLUMBER:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    pages_text.append(text)
    else:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text = page.extract_text()
                if text:
                    pages_text.append(text)
    
    return pages_text

def clean_text(text: str) -> str:
    """清理文本中的特殊字符"""
    if not text:
        return text
    
    # 替换常见的PDF解析错误
    replacements = {
        '\u0000': 'fi',
        'Con\u0000gure': 'Configure',
        'pro\u0000le': 'profile',
        '\u0000le': 'file',
        '\u0000les': 'files',
        'tra\u0000c': 'traffic',
        'certi\u0000cate': 'certificate',
        'noti\u0000cation': 'notification',
        'inde\u0000nitely': 'indefinitely',
        'con\u0000dential': 'confidential',
        'speci\u0000c': 'specific',
        'identi\u0000er': 'identifier',
        'work\u0000ow': 'workflow',
        'e\u0000cient': 'efficient',
        'a\u0000nity': 'affinity',
        'ample': 'Simple',
    }
    
    cleaned_text = text
    for old, new in replacements.items():
        cleaned_text = cleaned_text.replace(old, new)
    
    # 修复AWS服务名称
    service_fixes = {
        r'Amazon Simple Queue Service': 'Amazon Simple Queue Service',
        r'Amazon SOS': 'Amazon SQS',
        r'Cross- Region': 'Cross-Region',
        r'Multi- AZ': 'Multi-AZ',
        r'on- premises': 'on-premises',
    }
    
    for pattern, replacement in service_fixes.items():
        cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=re.IGNORECASE)
    
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    return cleaned_text.strip()

def parse_questions(pages_text: List[str]) -> List[QuizQuestion]:
    """解析题目"""
    questions = []
    
    # 合并所有页面文本
    full_text = "\n".join(pages_text)
    
    # 按题目分割
    topic_pattern = r'Topic\s+(\d+)\s+Question\s+#(\d+)'
    parts = re.split(topic_pattern, full_text)
    
    for i in range(1, len(parts), 3):
        if i + 2 < len(parts):
            topic_num = parts[i]
            question_num = parts[i + 1]
            content = parts[i + 2]
            
            question = parse_single_question(topic_num, question_num, content)
            if question:
                questions.append(question)
    
    return questions

def parse_single_question(topic_num: str, question_num: str, content: str) -> Optional[QuizQuestion]:
    """解析单个题目"""
    question = QuizQuestion()
    question.topic = f"Topic {topic_num}"
    question.question_number = question_num
    question.raw_text = content[:1000]
    
    lines = content.split('\n')
    question_lines = []
    options = []
    
    collecting_question = True
    current_option = ""
    option_letter = ""
    
    # 提取答案信息
    correct_answer_match = re.search(r'Correct Answer:\s*([A-F])', content, re.IGNORECASE)
    if correct_answer_match:
        question.correct_answer = correct_answer_match.group(1)
    
    selected_answer_match = re.search(r'Selected Answer:\s*([A-F])', content, re.IGNORECASE)
    if selected_answer_match:
        question.selected_answer = selected_answer_match.group(1)
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 停止收集当遇到答案部分
        if re.match(r'(Selected Answer|Correct Answer):', line, re.IGNORECASE):
            if current_option and option_letter:
                clean_option = clean_text(current_option.strip())
                if clean_option:
                    options.append(f"{option_letter}. {clean_option}")
            break
        
        # 检查选项
        option_match = re.match(r'^([A-F])\.\s*(.*)', line)
        if option_match:
            collecting_question = False
            if current_option and option_letter:
                clean_option = clean_text(current_option.strip())
                if clean_option:
                    options.append(f"{option_letter}. {clean_option}")
            
            option_letter = option_match.group(1)
            current_option = option_match.group(2)
        elif not collecting_question and option_letter:
            # 继续当前选项
            current_option += " " + line
        elif collecting_question:
            # 检查多选指示
            if re.search(r'choose\s+(two|three|four|2|3|4)', line, re.IGNORECASE):
                question.is_multiple_choice = True
                match = re.search(r'choose\s+(two|three|four|2|3|4)', line, re.IGNORECASE)
                if match:
                    choice_word = match.group(1).lower()
                    if choice_word in ['two', '2']:
                        question.multiple_choice_count = 2
                    elif choice_word in ['three', '3']:
                        question.multiple_choice_count = 3
                    elif choice_word in ['four', '4']:
                        question.multiple_choice_count = 4
            
            question_lines.append(line)
    
    # 添加最后一个选项
    if current_option and option_letter:
        clean_option = clean_text(current_option.strip())
        if clean_option:
            options.append(f"{option_letter}. {clean_option}")
    
    question.question_text = clean_text(" ".join(question_lines))
    question.options = options
    
    # 如果没有找到正确答案，尝试从selected answer推断
    if not question.correct_answer and question.selected_answer:
        question.correct_answer = question.selected_answer
    
    if not question.question_text or len(options) < 2:
        return None
    
    return question

def main():
    pdf_path = "SAA-C03 1019 quiz.pdf"
    
    print("正在提取PDF文本...")
    pages_text = extract_text_from_pdf(pdf_path)
    print(f"共提取了 {len(pages_text)} 页文本")
    
    print("正在解析题目和答案...")
    questions = parse_questions(pages_text)
    print(f"共解析出 {len(questions)} 道题目")
    
    # 统计信息
    single_choice = sum(1 for q in questions if not q.is_multiple_choice)
    multiple_choice = sum(1 for q in questions if q.is_multiple_choice)
    with_answers = sum(1 for q in questions if q.correct_answer)
    
    print(f"\n统计信息:")
    print(f"单选题: {single_choice}")
    print(f"多选题: {multiple_choice}")
    print(f"有答案的题目: {with_answers}")
    
    # 保存结果
    output_data = []
    for q in questions:
        output_data.append({
            "topic": q.topic,
            "question_number": q.question_number,
            "question_text": q.question_text,
            "options": q.options,
            "is_multiple_choice": q.is_multiple_choice,
            "multiple_choice_count": q.multiple_choice_count,
            "correct_answer": q.correct_answer,
            "selected_answer": q.selected_answer,
        })
    
    with open("aws_quiz_with_answers.json", "w", encoding="utf-8") as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到 aws_quiz_with_answers.json")
    
    # 显示前几道题目作为示例
    print(f"\n前3道题目示例:")
    for i, q in enumerate(questions[:3]):
        print(f"\n{i+1}. {q.topic} Question #{q.question_number}")
        print(f"类型: {'多选题' if q.is_multiple_choice else '单选题'}")
        if q.is_multiple_choice:
            print(f"选择数量: {q.multiple_choice_count}")
        print(f"题目: {q.question_text[:150]}...")
        print(f"选项数量: {len(q.options)}")
        for opt in q.options:
            print(f"  {opt[:80]}...")
        if q.correct_answer:
            print(f"正确答案: {q.correct_answer}")
        if q.selected_answer and q.selected_answer != q.correct_answer:
            print(f"选择答案: {q.selected_answer}")

if __name__ == "__main__":
    main()
