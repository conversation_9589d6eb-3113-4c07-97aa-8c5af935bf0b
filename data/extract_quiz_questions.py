#!/usr/bin/env python3
"""
AWS Quiz PDF Parser
解析AWS SAA-C03题库PDF文件，提取题目、选项和多选信息
"""

import re
import json
from typing import List, Dict, Optional
import sys

try:
    import pdfplumber
except ImportError:
    try:
        import PyPDF2
        USE_PDFPLUMBER = False
    except ImportError:
        print("请安装 pdfplumber 或 PyPDF2: pip install pdfplumber")
        sys.exit(1)
    else:
        USE_PDFPLUMBER = False
else:
    USE_PDFPLUMBER = True

class QuizQuestion:
    def __init__(self):
        self.topic = ""
        self.question_number = ""
        self.question_text = ""
        self.options = []
        self.is_multiple_choice = False
        self.multiple_choice_count = 0
        self.raw_text = ""

def extract_text_from_pdf(pdf_path: str) -> List[str]:
    """从PDF中提取所有页面的文本"""
    pages_text = []
    
    if USE_PDFPLUMBER:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    pages_text.append(text)
    else:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text = page.extract_text()
                if text:
                    pages_text.append(text)
    
    return pages_text

def parse_questions(pages_text: List[str]) -> List[QuizQuestion]:
    """解析题目"""
    questions = []
    current_question = None
    
    # 合并所有页面文本
    full_text = "\n".join(pages_text)
    
    # 分割成题目块
    # 寻找 "Topic X Question #Y" 模式
    topic_pattern = r'Topic\s+(\d+)\s+Question\s+#(\d+)'
    
    # 按题目分割
    parts = re.split(topic_pattern, full_text)
    
    for i in range(1, len(parts), 3):  # 每3个元素为一组：topic_num, question_num, content
        if i + 2 < len(parts):
            topic_num = parts[i]
            question_num = parts[i + 1]
            content = parts[i + 2]
            
            question = parse_single_question(topic_num, question_num, content)
            if question:
                questions.append(question)
    
    return questions

def parse_single_question(topic_num: str, question_num: str, content: str) -> Optional[QuizQuestion]:
    """解析单个题目"""
    question = QuizQuestion()
    question.topic = f"Topic {topic_num}"
    question.question_number = question_num
    question.raw_text = content[:500]  # 保存原始文本片段

    # 提取题目文本（从开始到第一个选项A.）
    lines = content.split('\n')
    question_lines = []
    options = []

    collecting_question = True
    current_option = ""
    option_letter = ""

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 跳过答案讨论部分
        if any(keyword in line.lower() for keyword in ['selected answer:', 'correct answer:', 'community vote', 'upvoted', 'highly voted', 'most recent']):
            break

        # 检查是否是选项（A. B. C. D. E. F.）
        option_match = re.match(r'^([A-F])\.\s*(.*)', line)
        if option_match:
            collecting_question = False
            if current_option and option_letter:
                # 清理选项文本，移除答案相关内容
                clean_option = clean_option_text(current_option.strip())
                if clean_option:
                    options.append(f"{option_letter}. {clean_option}")

            option_letter = option_match.group(1)
            current_option = option_match.group(2)
        elif not collecting_question and option_letter:
            # 继续当前选项的内容，但要检查是否遇到了答案部分
            if any(keyword in line.lower() for keyword in ['correct answer:', 'selected answer:', 'upvoted', 'highly voted']):
                break
            current_option += " " + line
        elif collecting_question:
            # 检查是否包含多选指示
            if re.search(r'choose\s+(two|three|four|2|3|4)', line, re.IGNORECASE):
                question.is_multiple_choice = True
                # 提取数量
                match = re.search(r'choose\s+(two|three|four|2|3|4)', line, re.IGNORECASE)
                if match:
                    choice_word = match.group(1).lower()
                    if choice_word in ['two', '2']:
                        question.multiple_choice_count = 2
                    elif choice_word in ['three', '3']:
                        question.multiple_choice_count = 3
                    elif choice_word in ['four', '4']:
                        question.multiple_choice_count = 4

            question_lines.append(line)

    # 添加最后一个选项
    if current_option and option_letter:
        clean_option = clean_option_text(current_option.strip())
        if clean_option:
            options.append(f"{option_letter}. {clean_option}")

    question.question_text = " ".join(question_lines).strip()
    question.options = options

    # 如果没有找到题目文本或选项，跳过
    if not question.question_text or len(options) < 2:
        return None

    return question

def clean_option_text(text: str) -> str:
    """清理选项文本，移除答案和讨论内容"""
    # 移除答案相关的标记
    text = re.sub(r'Correct Answer:.*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Selected Answer:.*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'upvoted \d+ times.*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Highly Voted.*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Most Recent.*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'Community vote.*', '', text, flags=re.IGNORECASE)

    # 移除特殊字符和多余空格
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    return text

def main():
    pdf_path = "SAA-C03 1019 quiz.pdf"
    
    print("正在提取PDF文本...")
    pages_text = extract_text_from_pdf(pdf_path)
    print(f"共提取了 {len(pages_text)} 页文本")
    
    print("正在解析题目...")
    questions = parse_questions(pages_text)
    print(f"共解析出 {len(questions)} 道题目")
    
    # 统计信息
    single_choice = sum(1 for q in questions if not q.is_multiple_choice)
    multiple_choice = sum(1 for q in questions if q.is_multiple_choice)
    
    print(f"\n统计信息:")
    print(f"单选题: {single_choice}")
    print(f"多选题: {multiple_choice}")
    
    # 保存结果
    output_data = []
    for q in questions:
        output_data.append({
            "topic": q.topic,
            "question_number": q.question_number,
            "question_text": q.question_text,
            "options": q.options,
            "is_multiple_choice": q.is_multiple_choice,
            "multiple_choice_count": q.multiple_choice_count,
            "raw_text_sample": q.raw_text
        })
    
    with open("aws_quiz_questions.json", "w", encoding="utf-8") as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到 aws_quiz_questions.json")
    
    # 显示前几道题目作为示例
    print(f"\n前5道题目示例:")
    for i, q in enumerate(questions[:5]):
        print(f"\n{i+1}. {q.topic} Question #{q.question_number}")
        print(f"类型: {'多选题' if q.is_multiple_choice else '单选题'}")
        if q.is_multiple_choice:
            print(f"选择数量: {q.multiple_choice_count}")
        print(f"题目: {q.question_text[:200]}...")
        print(f"选项数量: {len(q.options)}")
        for opt in q.options[:3]:  # 只显示前3个选项
            print(f"  {opt[:100]}...")

if __name__ == "__main__":
    main()
